%! Auswahl des Dokumententyps
%\documentclass[studentpaper]{wbk}
\documentclass[oneside,studentpaper]{wbk}

%%Als Encoding ist UTF-8 vorzuziehen
%%Dies erlaubt die direkte Verwendung von Umlauten etc. im Text.
%%Die bib-Datei (Bibtex) wird trotzdem mit Escape-Zeichen formatiert.


%% Folgende Klassen sind nicht zwingend Teil der wbk-Vorlage,
%% sondern je nach Dokument noetig oder auch nicht.
\usepackage{textcomp,booktabs,microtype}
\usepackage{units}
\usepackage{color}
\usepackage{tikz}
\usepackage{chemfig}
\usepackage{transparent}
\usepackage{caption}
\usepackage{subcaption}
\usepackage{amsmath}
\usepackage{float}
\usepackage{tabularray}
\usepackage{graphicx}
\usepackage{xcolor}
\usepackage{rotating}
\usepackage{diagbox}
\usepackage{svg}
\graphicspath{{Bilder/}}
\NewDocumentCommand{\Quote}{ m }{\glqq{}#1\grqq{}}
\usepackage[utf8]{inputenc}
\usepackage[T1]{fontenc}

% Definition der KIT-Farben
\definecolor{Grün}{RGB}{0,150,130}
\definecolor{Blau}{RGB}{70,100,170}
\definecolor{Grau}{RGB}{64,64,64}
\definecolor{Hellgrau}{RGB}{242,242,242}
\definecolor{Weiß}{RGB}{255,255,255}
\definecolor{Dunkel}{RGB}{203,221,216}
\definecolor{Hell}{RGB}{231,239,237}
\definecolor{OrangeKIT}{RGB}{223,155,27}
\definecolor{Maigrün}{RGB}{140,182,60}
\definecolor{CyanKIT}{RGB}{35,161,224}

% Einstellungen für Tabellen
\captionsetup[table]{justification=raggedright, singlelinecheck=true, position=above, font = it, labelfont = it}

% Einstellungen für Bilder
\captionsetup[figure]{justification=raggedright, singlelinecheck=true, position=below, font = it, labelfont = it}

%! Zwingend benoetigte Angaben.
	\author{Manuel Raimann}
	\authorismale
	\title{Abschlussarbeit: Entwicklung und Integration eines Systems zur echtzeitfähigen Prozess- und Modellüberwachung basierend auf prädiktiven Signalanalysen}
	%\subtitle{Erstellt mit \LaTeX}
	\placeanddate{Karlsruhe, 14.11.2025}

% %! Dieser Block ist nur bei Dissertationen auszufüllen.
% 	\birthplace{Entenhausen}
	\examdate{14.11.2025}
% 	\mainreferent{Prof. Dr.-Ing. XYZ}
% 	\coreferent{Prof. Dr.-Math. ABC-DEF}
% 	\volumenum{123}

%! Dieser Block ist nur bei Studentenarbeiten auszufüllen.
	\papertype{Masterarbeit}
	%% Sperrvermerke. Ist kein Sperrvermerk erwuenscht, sind diese Befehle nicht zu verwenden.
	%\disclosuredate{2022-08-24}
	%\disclosurecompany{wbk}
	\matrnumber{2283096}
	\authoraddress{Sophienstraße 123, 76135 Karlsruhe}
	\handindate{14.05.2025}
	\handoutdate{14.11.2025}
	\papernumber{MAP ????}
	\mainreferent{Prof. Dr.-Ing. Jürgen Fleischer}


	\setlength{\bibsep}{\baselineskip} % erzeugt eine leerzeile zwischen den bibliographieeintraegen 


\begin{document}
%? Hier muss die richtige Sprache ausgewaehlt werden. Die andere wird auskommentiert.
	\selectlanguage{ngerman}
	%\selectlanguage{english}
	\setlength{\mathindent}{5pt}
\maketitle

\newpage
\thispagestyle{empty}
~
\newpage

%! Dieser Block ist nur bei Dissertationen auszufüllen.
\ifDiss
	
%! Dieser Block ist nur bei Studentenarbeiten auszufüllen.
\else
	\statementoforiginality

	\begin{acknowledgment}

		ACKNOWLEDGEMENT

	\end{acknowledgment}
	
	\begin{summary}

		SUMMARY

	\end{summary}
\fi

% %! Hier steht das abstract auf Englisch
	\begin{abstract}

		ABSTRACT

	\end{abstract}



% Einleitende Kapitel sind im frontmatter, die Seiten roemisch durchnummeriert.
\frontmatter
\setstretch{1.2}
\tableofcontents
\setstretch{1.0}
\include{Kapitel/Abkürzungen}

% Der Hauptteil der Arbeit ist im mainmatter. Hier beginnen die eigentlichen Seitenzahlen.
\mainmatter

\include{Kapitel/01.Einleitung}
\include{Kapitel/02.Grundlagen}
\include{Kapitel/03.StandderTechnik}
\include{Kapitel/04.EigenerAnsatz}
\include{Kapitel/05.Versuchsdurchführung}
\include{Kapitel/06.Versuchsauswertung}
\include{Kapitel/07.DiskussionUndBewertung}
\include{Kapitel/08.Anwendung}
\include{Kapitel/09.ZusammenfassungUndAusblick}

% Der Schlussteil der Arbeit ist im backmatter. Hier beginnen wieder römische Seitenzahlen.
\backmatter

 % fügt alle in der bibliographie vorhandenen quellen ein, auch wenn sie nicht zitiert wurden


        


% Erzeugt das Abbildungsverzeichnis
	\setstretch{1.2}
	%\listoffigures
	{%
		\let\oldnumberline\numberline%
		\renewcommand{\numberline}{\figurename~\oldnumberline}%
		\listoffigures%
	}

% Erzeug ein Tabellenverzeichnis

\newpage
\setstretch{1.2}
{%
    \let\oldnumberline\numberline%
    \renewcommand{\numberline}{\tablename~\oldnumberline}%
    \listoftables%
}

% Erzeugt das Literaturverzeichnis
	\cleardoublepage
	\phantomsection
	\addcontentsline{toc}{chapter}{\bibname}
%! Hier den namen der *.bib Datei angeben um die eingene Bibliographie zu laden

    \bibliography{references}

%\chapter{Anhang}

\newpage
\thispagestyle{empty}
~
\newpage




% %! Dieser Block mit dem Lebenslauf ist nur bei Dissertationen auszufüllen.
% 	\ifDiss
% 		\begin{vita}
				
% 		\end{vita}
% 	\fi
\end{document}
