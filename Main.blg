This is BibTeX, Version 0.99d (TeX Live 2025/dev/Debian)
Capacity: max_strings=200000, hash_size=200000, hash_prime=170003
The top-level auxiliary file: Main.aux
The style file: wbk.bst
A level-1 auxiliary file: Kapitel/Abkürzungen.aux
A level-1 auxiliary file: Kapitel/01.Einleitung.aux
A level-1 auxiliary file: Kapitel/02.Grundlagen.aux
A level-1 auxiliary file: Kapitel/03.StandderTechnik.aux
A level-1 auxiliary file: Kapitel/04.EigenerAnsatz.aux
A level-1 auxiliary file: Kapitel/05.Versuchsdurchführung.aux
A level-1 auxiliary file: Kapitel/06.Versuchsauswertung.aux
A level-1 auxiliary file: Kapitel/07.DiskussionUndBewertung.aux
A level-1 auxiliary file: Kapitel/08.Anwendung.aux
A level-1 auxiliary file: Kapitel/09.ZusammenfassungUndAusblick.aux
I found no \citation commands---while reading file Main.aux
Database file #1: references.bib
Reallocated wiz_functions (elt_size=4) to 6000 items from 3000.
You've used 0 entries,
            3244 wiz_defined-function locations,
            663 strings with 5499 characters,
and the built_in function-call counts, 35 in all, are:
= -- 0
> -- 0
< -- 0
+ -- 0
- -- 1
* -- 2
:= -- 10
add.period$ -- 0
call.type$ -- 0
change.case$ -- 0
chr.to.int$ -- 1
cite$ -- 0
duplicate$ -- 0
empty$ -- 1
format.name$ -- 0
if$ -- 1
int.to.chr$ -- 1
int.to.str$ -- 1
missing$ -- 0
newline$ -- 8
num.names$ -- 0
pop$ -- 0
preamble$ -- 1
purify$ -- 0
quote$ -- 0
skip$ -- 1
stack$ -- 0
substring$ -- 0
swap$ -- 0
text.length$ -- 0
text.prefix$ -- 0
top$ -- 0
type$ -- 0
warning$ -- 0
while$ -- 0
width$ -- 0
write$ -- 7
(There was 1 error message)
