# GitLab CI/CD Configuration for LaTeX Thesis Compilation
# This configuration compiles a German LaTeX thesis using the custom wbk document class

# Define stages for the pipeline
stages:
  - build
  - deploy

# Global variables
variables:
  # Main LaTeX document file (without .tex extension)
  MAIN_FILE: "Main"
  # Output directory for compiled files
  OUTPUT_DIR: "out"
  # Cache key for LaTeX packages and auxiliary files
  CACHE_KEY: "latex-cache-v1"

# Cache configuration to speed up builds
cache:
  key: ${CACHE_KEY}
  paths:
    - ${OUTPUT_DIR}/
    - .texlive/
  policy: pull-push

# Main build job for compiling the LaTeX document
compile_latex:
  stage: build
  
  # Use comprehensive TeXLive Docker image with all packages
  image: texlive/texlive:latest
  
  # Define when this job should run (only on relevant file changes)
  rules:
    - if: $CI_COMMIT_BRANCH == $CI_DEFAULT_BRANCH
    - if: $CI_MERGE_REQUEST_IID
    - changes:
        - "*.tex"
        - "*.cls"
        - "*.bst"
        - "*.bib"
        - "Kapitel/*.tex"
        - "Bilder/**/*"
        - ".gitlab-ci.yml"
  
  # Set up environment and compile document
  script:
    - echo "Starting LaTeX compilation for ${MAIN_FILE}.tex"
    
    # Create output directory if it doesn't exist
    - mkdir -p ${OUTPUT_DIR}
    
    # Set TEXMFHOME for user packages (if needed)
    - export TEXMFHOME=.texlive
    - mkdir -p ${TEXMFHOME}
    
    # Display TeXLive version and available engines
    - echo "TeXLive version:"
    - tex --version | head -1
    - echo "Available engines:"
    - which pdflatex xelatex lualatex bibtex biber || true
    
    # First compilation pass
    - echo "=== First LaTeX compilation pass ==="
    - pdflatex -interaction=nonstopmode -output-directory=${OUTPUT_DIR} ${MAIN_FILE}.tex
    
    # Check if bibliography file exists and has content
    - |
      if [ -s references.bib ]; then
        echo "=== Running BibTeX for bibliography ==="
        cd ${OUTPUT_DIR}
        bibtex ${MAIN_FILE}
        cd ..
      else
        echo "Bibliography file is empty or missing, skipping BibTeX"
      fi
    
    # Second compilation pass (for bibliography references)
    - echo "=== Second LaTeX compilation pass ==="
    - pdflatex -interaction=nonstopmode -output-directory=${OUTPUT_DIR} ${MAIN_FILE}.tex
    
    # Third compilation pass (for cross-references and TOC)
    - echo "=== Third LaTeX compilation pass ==="
    - pdflatex -interaction=nonstopmode -output-directory=${OUTPUT_DIR} ${MAIN_FILE}.tex
    
    # Verify PDF was created successfully
    - |
      if [ -f "${OUTPUT_DIR}/${MAIN_FILE}.pdf" ]; then
        echo "✅ PDF compilation successful!"
        echo "PDF file size: $(du -h ${OUTPUT_DIR}/${MAIN_FILE}.pdf | cut -f1)"
        echo "PDF pages: $(pdfinfo ${OUTPUT_DIR}/${MAIN_FILE}.pdf | grep Pages | awk '{print $2}')"
      else
        echo "❌ PDF compilation failed!"
        echo "=== LaTeX Log Output ==="
        cat ${OUTPUT_DIR}/${MAIN_FILE}.log || true
        exit 1
      fi
    
    # Display compilation statistics
    - echo "=== Compilation Statistics ==="
    - echo "Generated files in ${OUTPUT_DIR}:"
    - ls -la ${OUTPUT_DIR}/
    
    # Check for LaTeX warnings in log file
    - |
      if [ -f "${OUTPUT_DIR}/${MAIN_FILE}.log" ]; then
        echo "=== Checking for LaTeX warnings ==="
        if grep -i "warning\|error" ${OUTPUT_DIR}/${MAIN_FILE}.log; then
          echo "⚠️  Warnings or errors found in compilation log"
        else
          echo "✅ No warnings or errors detected"
        fi
      fi
  
  # Define build artifacts
  artifacts:
    name: "${MAIN_FILE}-${CI_COMMIT_SHORT_SHA}"
    paths:
      - ${OUTPUT_DIR}/${MAIN_FILE}.pdf
      - ${OUTPUT_DIR}/${MAIN_FILE}.log
    reports:
      # Include log file for debugging
      junit: []
    expire_in: 30 days
    when: always
  
  # Retry configuration for transient failures
  retry:
    max: 2
    when:
      - runner_system_failure
      - stuck_or_timeout_failure

# Optional: Deploy job for releases (only runs on tags)
deploy_release:
  stage: deploy
  
  # Use minimal Alpine image for deployment tasks
  image: alpine:latest
  
  # Only run on git tags (releases)
  rules:
    - if: $CI_COMMIT_TAG
  
  # Depends on successful compilation
  dependencies:
    - compile_latex
  
  script:
    - echo "Deploying release ${CI_COMMIT_TAG}"
    - echo "PDF artifact: ${OUTPUT_DIR}/${MAIN_FILE}.pdf"
    
    # Install curl for potential upload to external services
    - apk add --no-cache curl
    
    # Rename PDF with version tag
    - cp ${OUTPUT_DIR}/${MAIN_FILE}.pdf ${MAIN_FILE}-${CI_COMMIT_TAG}.pdf
    
    # Here you could add commands to upload to external storage
    # Example: curl -F "file=@${MAIN_FILE}-${CI_COMMIT_TAG}.pdf" https://your-storage-service.com/upload
    
    - echo "Release deployment completed"
  
  artifacts:
    name: "${MAIN_FILE}-release-${CI_COMMIT_TAG}"
    paths:
      - ${MAIN_FILE}-${CI_COMMIT_TAG}.pdf
    expire_in: 1 year

# Optional: Lint job to check LaTeX syntax (runs in parallel)
lint_latex:
  stage: build
  
  image: texlive/texlive:latest
  
  # Run on all commits but allow failure
  rules:
    - if: $CI_COMMIT_BRANCH == $CI_DEFAULT_BRANCH
      allow_failure: true
    - if: $CI_MERGE_REQUEST_IID
      allow_failure: true
    - changes:
        - "*.tex"
        - "Kapitel/*.tex"
      allow_failure: true
  
  script:
    - echo "Running LaTeX syntax checks"
    
    # Install chktex if available (LaTeX linter)
    - which chktex || echo "chktex not available, skipping detailed linting"
    
    # Basic syntax check with lacheck
    - |
      if which lacheck >/dev/null 2>&1; then
        echo "=== Running lacheck on main file ==="
        lacheck ${MAIN_FILE}.tex || true
        
        echo "=== Running lacheck on chapter files ==="
        for file in Kapitel/*.tex; do
          if [ -f "$file" ]; then
            echo "Checking $file"
            lacheck "$file" || true
          fi
        done
      else
        echo "lacheck not available, performing basic checks"
      fi
    
    # Check for common LaTeX issues
    - echo "=== Checking for common issues ==="
    - grep -n "\\\\cite{}" *.tex Kapitel/*.tex || echo "No empty citations found"
    - grep -n "\\\\ref{}" *.tex Kapitel/*.tex || echo "No empty references found"
    - grep -n "TODO\|FIXME\|XXX" *.tex Kapitel/*.tex || echo "No TODO markers found"
  
  # Allow this job to fail without failing the pipeline
  allow_failure: true
