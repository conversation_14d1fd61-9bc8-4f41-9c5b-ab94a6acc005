This is pdfTeX, Version 3.141592653-2.6-1.40.26 (TeX Live 2025/dev/Debian) (preloaded format=pdflatex 2025.6.1)  1 JUN 2025 17:37
entering extended mode
 restricted \write18 enabled.
 %&-line parsing enabled.
**Main.tex
(./Main.tex
LaTeX2e <2024-11-01> patch level 2
L3 programming layer <2025-01-18>
(./wbk.cls
File: wbk.cls 

Document Class: wbk 2014/07/22 1.4.2 Templates for the wbk institute
(/usr/share/texlive/texmf-dist/tex/latex/koma-script/scrbook.cls
Document Class: scrbook 2024/10/24 v3.43 KOMA-Script document class (book)
(/usr/share/texlive/texmf-dist/tex/latex/koma-script/scrkbase.sty
Package: scrkbase 2024/10/24 v3.43 KOMA-Script package (KOMA-Script-dependent b
asics and keyval usage)

(/usr/share/texlive/texmf-dist/tex/latex/koma-script/scrbase.sty
Package: scrbase 2024/10/24 v3.43 KOMA-Script package (KOMA-Script-independent 
basics and keyval usage)

(/usr/share/texlive/texmf-dist/tex/latex/koma-script/scrlfile.sty
Package: scrlfile 2024/10/24 v3.43 KOMA-Script package (file load hooks)

(/usr/share/texlive/texmf-dist/tex/latex/koma-script/scrlfile-hook.sty
Package: scrlfile-hook 2024/10/24 v3.43 KOMA-Script package (using LaTeX hooks)


(/usr/share/texlive/texmf-dist/tex/latex/koma-script/scrlogo.sty
Package: scrlogo 2024/10/24 v3.43 KOMA-Script package (logo)
)))
(/usr/share/texlive/texmf-dist/tex/latex/graphics/keyval.sty
Package: keyval 2022/05/29 v1.15 key=value parser (DPC)
\KV@toks@=\toks17
)
Applying: [2021/05/01] Usage of raw or classic option list on input line 254.
Already applied: [0000/00/00] Usage of raw or classic option list on input line
 370.
))
(/usr/share/texlive/texmf-dist/tex/latex/koma-script/tocbasic.sty
Package: tocbasic 2024/10/24 v3.43 KOMA-Script package (handling toc-files)
\scr@dte@tocline@numberwidth=\skip49
\scr@dte@tocline@numbox=\box52
)
Package tocbasic Info: omitting babel extension for `toc'
(tocbasic)             because of feature `nobabel' available
(tocbasic)             for `toc' on input line 137.
Class scrbook Info: You've used standard option `oneside'.
(scrbook)           This is correct!
(scrbook)           Internally I'm using `twoside=false'.
(scrbook)           If you'd like to set the option with \KOMAoptions,
(scrbook)           you'd have to use `twoside=false' there
(scrbook)           instead of `oneside', too.

(/usr/share/texlive/texmf-dist/tex/latex/koma-script/typearea.sty
Package: typearea 2024/10/24 v3.43 KOMA-Script package (type area)
\ta@bcor=\skip50
\ta@div=\count196
Package typearea Info: You've used standard option `oneside'.
(typearea)             This is correct!
(typearea)             Internally I'm using `twoside=false'.
(typearea)             If you'd like to set the option with \KOMAoptions,
(typearea)             you'd have to use `twoside=false' there
(typearea)             instead of `oneside', too.
\ta@hblk=\skip51
\ta@vblk=\skip52
\ta@temp=\skip53
\footheight=\skip54


Package typearea Warning: DIV for 12.3pt and used papersize
(typearea)                not defined!
(typearea)                Using DIV=calc.


Package typearea Info: DIV calculation for classic typearea. on input line 1802
.

Package typearea Info: These are the values describing the layout:
(typearea)             DIV  = 9
(typearea)             BCOR = 0.0pt
(typearea)             \paperwidth      = 597.50793pt
(typearea)              \textwidth      = 398.33862pt
(typearea)              DIV departure   = 0%
(typearea)              \evensidemargin = 27.31467pt
(typearea)              \oddsidemargin  = 27.31467pt
(typearea)             \paperheight     = 845.04694pt
(typearea)              \textheight     = 573.17863pt
(typearea)              \topmargin      = -18.96577pt
(typearea)              \headheight     = 18.44995pt
(typearea)              \headsep        = 22.13994pt
(typearea)              \topskip        = 12.3pt
(typearea)              \footskip       = 51.65987pt
(typearea)              \baselineskip   = 14.75996pt
(typearea)              on input line 1802.
)
\c@part=\count197
\c@chapter=\count198
\c@section=\count199
\c@subsection=\count266
\c@subsubsection=\count267
\c@paragraph=\count268
\c@subparagraph=\count269
\scr@dte@chapter@maxnumwidth=\skip55
Class scrbook Info: using compatibility default `afterindent=bysign'
(scrbook)           for `\chapter on input line 6016.
\scr@dte@section@maxnumwidth=\skip56
Class scrbook Info: using compatibility default `runin=bysign'
(scrbook)           for `\section on input line 6027.
Class scrbook Info: using compatibility default `afterindent=bysign'
(scrbook)           for `\section on input line 6027.
\scr@dte@part@maxnumwidth=\skip57
Class scrbook Info: using compatibility default `afterindent=true'
(scrbook)           for `\part on input line 6036.
\scr@dte@subsection@maxnumwidth=\skip58
Class scrbook Info: using compatibility default `runin=bysign'
(scrbook)           for `\subsection on input line 6046.
Class scrbook Info: using compatibility default `afterindent=bysign'
(scrbook)           for `\subsection on input line 6046.
\scr@dte@subsubsection@maxnumwidth=\skip59
Class scrbook Info: using compatibility default `runin=bysign'
(scrbook)           for `\subsubsection on input line 6056.
Class scrbook Info: using compatibility default `afterindent=bysign'
(scrbook)           for `\subsubsection on input line 6056.
\scr@dte@paragraph@maxnumwidth=\skip60
Class scrbook Info: using compatibility default `runin=bysign'
(scrbook)           for `\paragraph on input line 6067.
Class scrbook Info: using compatibility default `afterindent=bysign'
(scrbook)           for `\paragraph on input line 6067.
\scr@dte@subparagraph@maxnumwidth=\skip61
Class scrbook Info: using compatibility default `runin=bysign'
(scrbook)           for `\subparagraph on input line 6077.
Class scrbook Info: using compatibility default `afterindent=bysign'
(scrbook)           for `\subparagraph on input line 6077.
\abovecaptionskip=\skip62
\belowcaptionskip=\skip63
\c@pti@nb@sid@b@x=\box53
Package tocbasic Info: omitting babel extension for `lof'
(tocbasic)             because of feature `nobabel' available
(tocbasic)             for `lof' on input line 7271.
\scr@dte@figure@maxnumwidth=\skip64
\c@figure=\count270
Package tocbasic Info: omitting babel extension for `lot'
(tocbasic)             because of feature `nobabel' available
(tocbasic)             for `lot' on input line 7288.
\scr@dte@table@maxnumwidth=\skip65
\c@table=\count271
Class scrbook Info: Redefining `\numberline' on input line 7459.
\bibindent=\dimen141
) (/usr/share/texlive/texmf-dist/tex/latex/base/inputenc.sty
Package: inputenc 2024/02/08 v1.3d Input encoding file
\inpenc@prehook=\toks18
\inpenc@posthook=\toks19
)
(/home/<USER>/texmf/tex/latex/amsmath/amsmath.sty
Package: amsmath 2024/11/05 v2.17t AMS math features
\@mathmargin=\skip66

For additional information on amsmath, use the `?' option.
(/home/<USER>/texmf/tex/latex/amsmath/amstext.sty
Package: amstext 2021/08/26 v2.01 AMS text

(/home/<USER>/texmf/tex/latex/amsmath/amsgen.sty
File: amsgen.sty 1999/11/30 v2.0 generic functions
\@emptytoks=\toks20
\ex@=\dimen142
))
(/home/<USER>/texmf/tex/latex/amsmath/amsbsy.sty
Package: amsbsy 1999/11/29 v1.2d Bold Symbols
\pmbraise@=\dimen143
)
(/home/<USER>/texmf/tex/latex/amsmath/amsopn.sty
Package: amsopn 2022/04/08 v2.04 operator names
)
\inf@bad=\count272
LaTeX Info: Redefining \frac on input line 233.
\uproot@=\count273
\leftroot@=\count274
LaTeX Info: Redefining \overline on input line 398.
LaTeX Info: Redefining \colon on input line 409.
\classnum@=\count275
\DOTSCASE@=\count276
LaTeX Info: Redefining \ldots on input line 495.
LaTeX Info: Redefining \dots on input line 498.
LaTeX Info: Redefining \cdots on input line 619.
\Mathstrutbox@=\box54
\strutbox@=\box55
LaTeX Info: Redefining \big on input line 721.
LaTeX Info: Redefining \Big on input line 722.
LaTeX Info: Redefining \bigg on input line 723.
LaTeX Info: Redefining \Bigg on input line 724.
\big@size=\dimen144
LaTeX Font Info:    Redeclaring font encoding OML on input line 742.
LaTeX Font Info:    Redeclaring font encoding OMS on input line 743.
\macc@depth=\count277
LaTeX Info: Redefining \bmod on input line 904.
LaTeX Info: Redefining \pmod on input line 909.
LaTeX Info: Redefining \smash on input line 939.
LaTeX Info: Redefining \relbar on input line 969.
LaTeX Info: Redefining \Relbar on input line 970.
\c@MaxMatrixCols=\count278
\dotsspace@=\muskip17
\c@parentequation=\count279
\dspbrk@lvl=\count280
\tag@help=\toks21
\row@=\count281
\column@=\count282
\maxfields@=\count283
\andhelp@=\toks22
\eqnshift@=\dimen145
\alignsep@=\dimen146
\tagshift@=\dimen147
\tagwidth@=\dimen148
\totwidth@=\dimen149
\lineht@=\dimen150
\@envbody=\toks23
\multlinegap=\skip67
\multlinetaggap=\skip68
\mathdisplay@stack=\toks24
LaTeX Info: Redefining \[ on input line 2953.
LaTeX Info: Redefining \] on input line 2954.
)
(/usr/share/texlive/texmf-dist/tex/latex/mathtools/mathtools.sty
Package: mathtools 2024/10/04 v1.31 mathematical typesetting tools

(/usr/share/texlive/texmf-dist/tex/latex/tools/calc.sty
Package: calc 2023/07/08 v4.3 Infix arithmetic (KKT,FJ)
\calc@Acount=\count284
\calc@Bcount=\count285
\calc@Adimen=\dimen151
\calc@Bdimen=\dimen152
\calc@Askip=\skip69
\calc@Bskip=\skip70
LaTeX Info: Redefining \setlength on input line 80.
LaTeX Info: Redefining \addtolength on input line 81.
\calc@Ccount=\count286
\calc@Cskip=\skip71
)
(/usr/share/texlive/texmf-dist/tex/latex/mathtools/mhsetup.sty
Package: mhsetup 2021/03/18 v1.4 programming setup (MH)
)
\g_MT_multlinerow_int=\count287
\l_MT_multwidth_dim=\dimen153
\origjot=\skip72
\l_MT_shortvdotswithinadjustabove_dim=\dimen154
\l_MT_shortvdotswithinadjustbelow_dim=\dimen155
\l_MT_above_intertext_sep=\dimen156
\l_MT_below_intertext_sep=\dimen157
\l_MT_above_shortintertext_sep=\dimen158
\l_MT_below_shortintertext_sep=\dimen159
\xmathstrut@box=\box56
\xmathstrut@dim=\dimen160
)
(/usr/share/texlive/texmf-dist/tex/latex/base/fontenc.sty
Package: fontenc 2021/04/29 v2.0v Standard LaTeX package
)
(/usr/share/texlive/texmf-dist/tex/latex/base/fix-cm.sty
Package: fix-cm 2020/11/24 v1.1t fixes to LaTeX

(/usr/share/texlive/texmf-dist/tex/latex/base/ts1enc.def
File: ts1enc.def 2001/06/05 v3.0e (jk/car/fm) Standard LaTeX file
LaTeX Font Info:    Redeclaring font encoding TS1 on input line 47.
))
(/usr/share/texlive/texmf-dist/tex/generic/babel/babel.sty
Package: babel 2025/02/14 v25.4 The multilingual framework for pdfLaTeX, LuaLaT
eX and XeLaTeX
\babel@savecnt=\count288
\U@D=\dimen161
\l@unhyphenated=\language10

(/usr/share/texlive/texmf-dist/tex/generic/babel/txtbabel.def)
\bbl@readstream=\read2
\bbl@dirlevel=\count289

(/usr/share/texlive/texmf-dist/tex/generic/babel-english/english.ldf
Language: english 2017/06/06 v3.3r English support from the babel system
Package babel Info: Hyphen rules for 'canadian' set to \l@english
(babel)             (\language0). Reported on input line 102.
Package babel Info: Hyphen rules for 'australian' set to \l@ukenglish
(babel)             (\language5). Reported on input line 105.
Package babel Info: Hyphen rules for 'newzealand' set to \l@ukenglish
(babel)             (\language5). Reported on input line 108.
)
(/usr/share/texlive/texmf-dist/tex/generic/babel-german/ngerman.ldf
Language: ngerman 2024/12/10 v2.15 German support for babel (post-1996 orthogra
phy)

(/usr/share/texlive/texmf-dist/tex/generic/babel-german/ngermanb.ldf
Language: ngermanb 2024/12/10 v2.15 German support for babel (post-1996 orthogr
aphy)
Package babel Info: Making " an active character on input line 122.
)))
(/usr/share/texlive/texmf-dist/tex/generic/babel/locale/de/babel-ngerman.tex
Package babel Info: Importing font and identification data for ngerman
(babel)             from babel-de.ini. Reported on input line 11.
)
(/usr/share/texlive/texmf-dist/tex/generic/babel/locale/en/babel-english.tex
Package babel Info: Importing font and identification data for english
(babel)             from babel-en.ini. Reported on input line 11.
)
(/usr/share/texmf/tex/latex/lm/lmodern.sty
Package: lmodern 2015/05/01 v1.6.1 Latin Modern Fonts
LaTeX Font Info:    Overwriting symbol font `operators' in version `normal'
(Font)                  OT1/cmr/m/n --> OT1/lmr/m/n on input line 22.
LaTeX Font Info:    Overwriting symbol font `letters' in version `normal'
(Font)                  OML/cmm/m/it --> OML/lmm/m/it on input line 23.
LaTeX Font Info:    Overwriting symbol font `symbols' in version `normal'
(Font)                  OMS/cmsy/m/n --> OMS/lmsy/m/n on input line 24.
LaTeX Font Info:    Overwriting symbol font `largesymbols' in version `normal'
(Font)                  OMX/cmex/m/n --> OMX/lmex/m/n on input line 25.
LaTeX Font Info:    Overwriting symbol font `operators' in version `bold'
(Font)                  OT1/cmr/bx/n --> OT1/lmr/bx/n on input line 26.
LaTeX Font Info:    Overwriting symbol font `letters' in version `bold'
(Font)                  OML/cmm/b/it --> OML/lmm/b/it on input line 27.
LaTeX Font Info:    Overwriting symbol font `symbols' in version `bold'
(Font)                  OMS/cmsy/b/n --> OMS/lmsy/b/n on input line 28.
LaTeX Font Info:    Overwriting symbol font `largesymbols' in version `bold'
(Font)                  OMX/cmex/m/n --> OMX/lmex/m/n on input line 29.
LaTeX Font Info:    Overwriting math alphabet `\mathbf' in version `normal'
(Font)                  OT1/cmr/bx/n --> OT1/lmr/bx/n on input line 31.
LaTeX Font Info:    Overwriting math alphabet `\mathsf' in version `normal'
(Font)                  OT1/cmss/m/n --> OT1/lmss/m/n on input line 32.
LaTeX Font Info:    Overwriting math alphabet `\mathit' in version `normal'
(Font)                  OT1/cmr/m/it --> OT1/lmr/m/it on input line 33.
LaTeX Font Info:    Overwriting math alphabet `\mathtt' in version `normal'
(Font)                  OT1/cmtt/m/n --> OT1/lmtt/m/n on input line 34.
LaTeX Font Info:    Overwriting math alphabet `\mathbf' in version `bold'
(Font)                  OT1/cmr/bx/n --> OT1/lmr/bx/n on input line 35.
LaTeX Font Info:    Overwriting math alphabet `\mathsf' in version `bold'
(Font)                  OT1/cmss/bx/n --> OT1/lmss/bx/n on input line 36.
LaTeX Font Info:    Overwriting math alphabet `\mathit' in version `bold'
(Font)                  OT1/cmr/bx/it --> OT1/lmr/bx/it on input line 37.
LaTeX Font Info:    Overwriting math alphabet `\mathtt' in version `bold'
(Font)                  OT1/cmtt/m/n --> OT1/lmtt/m/n on input line 38.
)
(/usr/share/texlive/texmf-dist/tex/latex/eso-pic/eso-pic.sty
Package: eso-pic 2023/05/03 v3.0c eso-pic (RN)
\ESO@tempdima=\dimen162
\ESO@tempdimb=\dimen163

(/home/<USER>/texmf/tex/latex/xcolor/xcolor.sty
Package: xcolor 2024/09/29 v3.02 LaTeX color extensions (UK)

(/usr/share/texlive/texmf-dist/tex/latex/graphics-cfg/color.cfg
File: color.cfg 2016/01/02 v1.6 sample color configuration
)
Package xcolor Info: Driver file: pdftex.def on input line 274.

(/usr/share/texlive/texmf-dist/tex/latex/graphics-def/pdftex.def
File: pdftex.def 2024/04/13 v1.2c Graphics/color driver for pdftex
)
(/usr/share/texlive/texmf-dist/tex/latex/graphics/mathcolor.ltx)
Package xcolor Info: Model `cmy' substituted by `cmy0' on input line 1349.
Package xcolor Info: Model `hsb' substituted by `rgb' on input line 1353.
Package xcolor Info: Model `RGB' extended on input line 1365.
Package xcolor Info: Model `HTML' substituted by `rgb' on input line 1367.
Package xcolor Info: Model `Hsb' substituted by `hsb' on input line 1368.
Package xcolor Info: Model `tHsb' substituted by `hsb' on input line 1369.
Package xcolor Info: Model `HSB' substituted by `hsb' on input line 1370.
Package xcolor Info: Model `Gray' substituted by `gray' on input line 1371.
Package xcolor Info: Model `wave' substituted by `hsb' on input line 1372.
))
(/usr/share/texlive/texmf-dist/tex/latex/psnfss/helvet.sty
Package: helvet 2020/03/25 PSNFSS-v9.3 (WaS) 
)
(/usr/share/texlive/texmf-dist/tex/latex/pgf/basiclayer/pgf.sty
(/usr/share/texlive/texmf-dist/tex/latex/pgf/utilities/pgfrcs.sty
(/usr/share/texlive/texmf-dist/tex/generic/pgf/utilities/pgfutil-common.tex
\pgfutil@everybye=\toks25
\pgfutil@tempdima=\dimen164
\pgfutil@tempdimb=\dimen165
)
(/usr/share/texlive/texmf-dist/tex/generic/pgf/utilities/pgfutil-latex.def
\pgfutil@abb=\box57
)
(/usr/share/texlive/texmf-dist/tex/generic/pgf/utilities/pgfrcs.code.tex
(/usr/share/texlive/texmf-dist/tex/generic/pgf/pgf.revision.tex)
Package: pgfrcs 2023-01-15 v3.1.10 (3.1.10)
))
Package: pgf 2023-01-15 v3.1.10 (3.1.10)

(/usr/share/texlive/texmf-dist/tex/latex/pgf/basiclayer/pgfcore.sty
(/usr/share/texlive/texmf-dist/tex/latex/graphics/graphicx.sty
Package: graphicx 2021/09/16 v1.2d Enhanced LaTeX Graphics (DPC,SPQR)

(/usr/share/texlive/texmf-dist/tex/latex/graphics/graphics.sty
Package: graphics 2024/08/06 v1.4g Standard LaTeX Graphics (DPC,SPQR)

(/usr/share/texlive/texmf-dist/tex/latex/graphics/trig.sty
Package: trig 2023/12/02 v1.11 sin cos tan (DPC)
)
(/usr/share/texlive/texmf-dist/tex/latex/graphics-cfg/graphics.cfg
File: graphics.cfg 2016/06/04 v1.11 sample graphics configuration
)
Package graphics Info: Driver file: pdftex.def on input line 106.
)
\Gin@req@height=\dimen166
\Gin@req@width=\dimen167
)
(/usr/share/texlive/texmf-dist/tex/latex/pgf/systemlayer/pgfsys.sty
(/usr/share/texlive/texmf-dist/tex/generic/pgf/systemlayer/pgfsys.code.tex
Package: pgfsys 2023-01-15 v3.1.10 (3.1.10)

(/usr/share/texlive/texmf-dist/tex/generic/pgf/utilities/pgfkeys.code.tex
\pgfkeys@pathtoks=\toks26
\pgfkeys@temptoks=\toks27

(/usr/share/texlive/texmf-dist/tex/generic/pgf/utilities/pgfkeyslibraryfiltered
.code.tex
\pgfkeys@tmptoks=\toks28
))
\pgf@x=\dimen168
\pgf@y=\dimen169
\pgf@xa=\dimen170
\pgf@ya=\dimen171
\pgf@xb=\dimen172
\pgf@yb=\dimen173
\pgf@xc=\dimen174
\pgf@yc=\dimen175
\pgf@xd=\dimen176
\pgf@yd=\dimen177
\w@pgf@writea=\write3
\r@pgf@reada=\read3
\c@pgf@counta=\count290
\c@pgf@countb=\count291
\c@pgf@countc=\count292
\c@pgf@countd=\count293
\t@pgf@toka=\toks29
\t@pgf@tokb=\toks30
\t@pgf@tokc=\toks31
\pgf@sys@id@count=\count294
 (/usr/share/texlive/texmf-dist/tex/generic/pgf/systemlayer/pgf.cfg
File: pgf.cfg 2023-01-15 v3.1.10 (3.1.10)
)
Driver file for pgf: pgfsys-pdftex.def
(/usr/share/texlive/texmf-dist/tex/generic/pgf/systemlayer/pgfsys-pdftex.def
File: pgfsys-pdftex.def 2023-01-15 v3.1.10 (3.1.10)

(/usr/share/texlive/texmf-dist/tex/generic/pgf/systemlayer/pgfsys-common-pdf.de
f
File: pgfsys-common-pdf.def 2023-01-15 v3.1.10 (3.1.10)
)))
(/usr/share/texlive/texmf-dist/tex/generic/pgf/systemlayer/pgfsyssoftpath.code.
tex
File: pgfsyssoftpath.code.tex 2023-01-15 v3.1.10 (3.1.10)
\pgfsyssoftpath@smallbuffer@items=\count295
\pgfsyssoftpath@bigbuffer@items=\count296
)
(/usr/share/texlive/texmf-dist/tex/generic/pgf/systemlayer/pgfsysprotocol.code.
tex
File: pgfsysprotocol.code.tex 2023-01-15 v3.1.10 (3.1.10)
))
(/usr/share/texlive/texmf-dist/tex/generic/pgf/basiclayer/pgfcore.code.tex
Package: pgfcore 2023-01-15 v3.1.10 (3.1.10)

(/usr/share/texlive/texmf-dist/tex/generic/pgf/math/pgfmath.code.tex
(/usr/share/texlive/texmf-dist/tex/generic/pgf/math/pgfmathutil.code.tex)
(/usr/share/texlive/texmf-dist/tex/generic/pgf/math/pgfmathparser.code.tex
\pgfmath@dimen=\dimen178
\pgfmath@count=\count297
\pgfmath@box=\box58
\pgfmath@toks=\toks32
\pgfmath@stack@operand=\toks33
\pgfmath@stack@operation=\toks34
)
(/usr/share/texlive/texmf-dist/tex/generic/pgf/math/pgfmathfunctions.code.tex)
(/usr/share/texlive/texmf-dist/tex/generic/pgf/math/pgfmathfunctions.basic.code
.tex)
(/usr/share/texlive/texmf-dist/tex/generic/pgf/math/pgfmathfunctions.trigonomet
ric.code.tex)
(/usr/share/texlive/texmf-dist/tex/generic/pgf/math/pgfmathfunctions.random.cod
e.tex)
(/usr/share/texlive/texmf-dist/tex/generic/pgf/math/pgfmathfunctions.comparison
.code.tex)
(/usr/share/texlive/texmf-dist/tex/generic/pgf/math/pgfmathfunctions.base.code.
tex)
(/usr/share/texlive/texmf-dist/tex/generic/pgf/math/pgfmathfunctions.round.code
.tex)
(/usr/share/texlive/texmf-dist/tex/generic/pgf/math/pgfmathfunctions.misc.code.
tex)
(/usr/share/texlive/texmf-dist/tex/generic/pgf/math/pgfmathfunctions.integerari
thmetics.code.tex)
(/usr/share/texlive/texmf-dist/tex/generic/pgf/math/pgfmathcalc.code.tex)
(/usr/share/texlive/texmf-dist/tex/generic/pgf/math/pgfmathfloat.code.tex
\c@pgfmathroundto@lastzeros=\count298
))
(/usr/share/texlive/texmf-dist/tex/generic/pgf/math/pgfint.code.tex)
(/usr/share/texlive/texmf-dist/tex/generic/pgf/basiclayer/pgfcorepoints.code.te
x
File: pgfcorepoints.code.tex 2023-01-15 v3.1.10 (3.1.10)
\pgf@picminx=\dimen179
\pgf@picmaxx=\dimen180
\pgf@picminy=\dimen181
\pgf@picmaxy=\dimen182
\pgf@pathminx=\dimen183
\pgf@pathmaxx=\dimen184
\pgf@pathminy=\dimen185
\pgf@pathmaxy=\dimen186
\pgf@xx=\dimen187
\pgf@xy=\dimen188
\pgf@yx=\dimen189
\pgf@yy=\dimen190
\pgf@zx=\dimen191
\pgf@zy=\dimen192
)
(/usr/share/texlive/texmf-dist/tex/generic/pgf/basiclayer/pgfcorepathconstruct.
code.tex
File: pgfcorepathconstruct.code.tex 2023-01-15 v3.1.10 (3.1.10)
\pgf@path@lastx=\dimen193
\pgf@path@lasty=\dimen194
)
(/usr/share/texlive/texmf-dist/tex/generic/pgf/basiclayer/pgfcorepathusage.code
.tex
File: pgfcorepathusage.code.tex 2023-01-15 v3.1.10 (3.1.10)
\pgf@shorten@end@additional=\dimen195
\pgf@shorten@start@additional=\dimen196
)
(/usr/share/texlive/texmf-dist/tex/generic/pgf/basiclayer/pgfcorescopes.code.te
x
File: pgfcorescopes.code.tex 2023-01-15 v3.1.10 (3.1.10)
\pgfpic=\box59
\pgf@hbox=\box60
\pgf@layerbox@main=\box61
\pgf@picture@serial@count=\count299
)
(/usr/share/texlive/texmf-dist/tex/generic/pgf/basiclayer/pgfcoregraphicstate.c
ode.tex
File: pgfcoregraphicstate.code.tex 2023-01-15 v3.1.10 (3.1.10)
\pgflinewidth=\dimen197
)
(/usr/share/texlive/texmf-dist/tex/generic/pgf/basiclayer/pgfcoretransformation
s.code.tex
File: pgfcoretransformations.code.tex 2023-01-15 v3.1.10 (3.1.10)
\pgf@pt@x=\dimen198
\pgf@pt@y=\dimen199
\pgf@pt@temp=\dimen256
)
(/usr/share/texlive/texmf-dist/tex/generic/pgf/basiclayer/pgfcorequick.code.tex
File: pgfcorequick.code.tex 2023-01-15 v3.1.10 (3.1.10)
)
(/usr/share/texlive/texmf-dist/tex/generic/pgf/basiclayer/pgfcoreobjects.code.t
ex
File: pgfcoreobjects.code.tex 2023-01-15 v3.1.10 (3.1.10)
)
(/usr/share/texlive/texmf-dist/tex/generic/pgf/basiclayer/pgfcorepathprocessing
.code.tex
File: pgfcorepathprocessing.code.tex 2023-01-15 v3.1.10 (3.1.10)
)
(/usr/share/texlive/texmf-dist/tex/generic/pgf/basiclayer/pgfcorearrows.code.te
x
File: pgfcorearrows.code.tex 2023-01-15 v3.1.10 (3.1.10)
\pgfarrowsep=\dimen257
)
(/usr/share/texlive/texmf-dist/tex/generic/pgf/basiclayer/pgfcoreshade.code.tex
File: pgfcoreshade.code.tex 2023-01-15 v3.1.10 (3.1.10)
\pgf@max=\dimen258
\pgf@sys@shading@range@num=\count300
\pgf@shadingcount=\count301
)
(/usr/share/texlive/texmf-dist/tex/generic/pgf/basiclayer/pgfcoreimage.code.tex
File: pgfcoreimage.code.tex 2023-01-15 v3.1.10 (3.1.10)
)
(/usr/share/texlive/texmf-dist/tex/generic/pgf/basiclayer/pgfcoreexternal.code.
tex
File: pgfcoreexternal.code.tex 2023-01-15 v3.1.10 (3.1.10)
\pgfexternal@startupbox=\box62
)
(/usr/share/texlive/texmf-dist/tex/generic/pgf/basiclayer/pgfcorelayers.code.te
x
File: pgfcorelayers.code.tex 2023-01-15 v3.1.10 (3.1.10)
)
(/usr/share/texlive/texmf-dist/tex/generic/pgf/basiclayer/pgfcoretransparency.c
ode.tex
File: pgfcoretransparency.code.tex 2023-01-15 v3.1.10 (3.1.10)
)
(/usr/share/texlive/texmf-dist/tex/generic/pgf/basiclayer/pgfcorepatterns.code.
tex
File: pgfcorepatterns.code.tex 2023-01-15 v3.1.10 (3.1.10)
)
(/usr/share/texlive/texmf-dist/tex/generic/pgf/basiclayer/pgfcorerdf.code.tex
File: pgfcorerdf.code.tex 2023-01-15 v3.1.10 (3.1.10)
)))
(/usr/share/texlive/texmf-dist/tex/generic/pgf/modules/pgfmoduleshapes.code.tex
File: pgfmoduleshapes.code.tex 2023-01-15 v3.1.10 (3.1.10)
\pgfnodeparttextbox=\box63
) (/usr/share/texlive/texmf-dist/tex/generic/pgf/modules/pgfmoduleplot.code.tex
File: pgfmoduleplot.code.tex 2023-01-15 v3.1.10 (3.1.10)
)
(/usr/share/texlive/texmf-dist/tex/latex/pgf/compatibility/pgfcomp-version-0-65
.sty
Package: pgfcomp-version-0-65 2023-01-15 v3.1.10 (3.1.10)
\pgf@nodesepstart=\dimen259
\pgf@nodesepend=\dimen260
)
(/usr/share/texlive/texmf-dist/tex/latex/pgf/compatibility/pgfcomp-version-1-18
.sty
Package: pgfcomp-version-1-18 2023-01-15 v3.1.10 (3.1.10)
)) (/usr/share/texlive/texmf-dist/tex/latex/setspace/setspace.sty
Package: setspace 2022/12/04 v6.7b set line spacing
)
(/usr/share/texlive/texmf-dist/tex/latex/natbib/natbib.sty
Package: natbib 2010/09/13 8.31b (PWD, AO)
\bibhang=\skip73
\bibsep=\skip74
LaTeX Info: Redefining \cite on input line 694.
\c@NAT@ctr=\count302
)
(/usr/share/texlive/texmf-dist/tex/latex/hyperref/hyperref.sty
Package: hyperref 2024-11-05 v7.01l Hypertext links for LaTeX

(/usr/share/texlive/texmf-dist/tex/generic/iftex/iftex.sty
Package: iftex 2024/12/12 v1.0g TeX engine tests
)
(/usr/share/texlive/texmf-dist/tex/latex/kvsetkeys/kvsetkeys.sty
Package: kvsetkeys 2022-10-05 v1.19 Key value parser (HO)
)
(/usr/share/texlive/texmf-dist/tex/generic/kvdefinekeys/kvdefinekeys.sty
Package: kvdefinekeys 2019-12-19 v1.6 Define keys (HO)
)
(/usr/share/texlive/texmf-dist/tex/generic/pdfescape/pdfescape.sty
Package: pdfescape 2019/12/09 v1.15 Implements pdfTeX's escape features (HO)

(/usr/share/texlive/texmf-dist/tex/generic/ltxcmds/ltxcmds.sty
Package: ltxcmds 2023-12-04 v1.26 LaTeX kernel commands for general use (HO)
)
(/usr/share/texlive/texmf-dist/tex/generic/pdftexcmds/pdftexcmds.sty
Package: pdftexcmds 2020-06-27 v0.33 Utility functions of pdfTeX for LuaTeX (HO
)

(/usr/share/texlive/texmf-dist/tex/generic/infwarerr/infwarerr.sty
Package: infwarerr 2019/12/03 v1.5 Providing info/warning/error messages (HO)
)
Package pdftexcmds Info: \pdf@primitive is available.
Package pdftexcmds Info: \pdf@ifprimitive is available.
Package pdftexcmds Info: \pdfdraftmode found.
))
(/usr/share/texlive/texmf-dist/tex/latex/hycolor/hycolor.sty
Package: hycolor 2020-01-27 v1.10 Color options for hyperref/bookmark (HO)
)
(/usr/share/texlive/texmf-dist/tex/latex/hyperref/nameref.sty
Package: nameref 2023-11-26 v2.56 Cross-referencing by name of section

(/usr/share/texlive/texmf-dist/tex/latex/refcount/refcount.sty
Package: refcount 2019/12/15 v3.6 Data extraction from label references (HO)
)
(/usr/share/texlive/texmf-dist/tex/generic/gettitlestring/gettitlestring.sty
Package: gettitlestring 2019/12/15 v1.6 Cleanup title references (HO)

(/usr/share/texlive/texmf-dist/tex/latex/kvoptions/kvoptions.sty
Package: kvoptions 2022-06-15 v3.15 Key value format for package options (HO)
))
\c@section@level=\count303
)
(/usr/share/texlive/texmf-dist/tex/latex/etoolbox/etoolbox.sty
Package: etoolbox 2025/02/11 v2.5l e-TeX tools for LaTeX (JAW)
\etb@tempcnta=\count304
)
(/usr/share/texlive/texmf-dist/tex/generic/stringenc/stringenc.sty
Package: stringenc 2019/11/29 v1.12 Convert strings between diff. encodings (HO
)
)
\@linkdim=\dimen261
\Hy@linkcounter=\count305
\Hy@pagecounter=\count306

(/usr/share/texlive/texmf-dist/tex/latex/hyperref/pd1enc.def
File: pd1enc.def 2024-11-05 v7.01l Hyperref: PDFDocEncoding definition (HO)
Now handling font encoding PD1 ...
... no UTF-8 mapping file for font encoding PD1
)
(/usr/share/texlive/texmf-dist/tex/generic/intcalc/intcalc.sty
Package: intcalc 2019/12/15 v1.3 Expandable calculations with integers (HO)
)
\Hy@SavedSpaceFactor=\count307

(/usr/share/texlive/texmf-dist/tex/latex/hyperref/puenc.def
File: puenc.def 2024-11-05 v7.01l Hyperref: PDF Unicode definition (HO)
Now handling font encoding PU ...
... no UTF-8 mapping file for font encoding PU
)
Package hyperref Info: Hyper figures OFF on input line 4157.
Package hyperref Info: Link nesting OFF on input line 4162.
Package hyperref Info: Hyper index ON on input line 4165.
Package hyperref Info: Plain pages OFF on input line 4172.
Package hyperref Info: Backreferencing OFF on input line 4177.
Package hyperref Info: Implicit mode ON; LaTeX internals redefined.
Package hyperref Info: Bookmarks ON on input line 4424.
\c@Hy@tempcnt=\count308

(/usr/share/texlive/texmf-dist/tex/latex/url/url.sty
\Urlmuskip=\muskip18
Package: url 2013/09/16  ver 3.4  Verb mode for urls, etc.
)
LaTeX Info: Redefining \url on input line 4763.
\XeTeXLinkMargin=\dimen262

(/usr/share/texlive/texmf-dist/tex/generic/bitset/bitset.sty
Package: bitset 2019/12/09 v1.3 Handle bit-vector datatype (HO)

(/usr/share/texlive/texmf-dist/tex/generic/bigintcalc/bigintcalc.sty
Package: bigintcalc 2019/12/15 v1.5 Expandable calculations on big integers (HO
)
))
\Fld@menulength=\count309
\Field@Width=\dimen263
\Fld@charsize=\dimen264
Package hyperref Info: Hyper figures OFF on input line 6042.
Package hyperref Info: Link nesting OFF on input line 6047.
Package hyperref Info: Hyper index ON on input line 6050.
Package hyperref Info: backreferencing OFF on input line 6057.
Package hyperref Info: Link coloring OFF on input line 6062.
Package hyperref Info: Link coloring with OCG OFF on input line 6067.
Package hyperref Info: PDF/A mode OFF on input line 6072.

(/usr/share/texlive/texmf-dist/tex/latex/base/atbegshi-ltx.sty
Package: atbegshi-ltx 2021/01/10 v1.0c Emulation of the original atbegshi
package with kernel methods
)
\Hy@abspage=\count310
\c@Item=\count311
\c@Hfootnote=\count312
)
Package hyperref Info: Driver (autodetected): hpdftex.

(/usr/share/texlive/texmf-dist/tex/latex/hyperref/hpdftex.def
File: hpdftex.def 2024-11-05 v7.01l Hyperref driver for pdfTeX

(/usr/share/texlive/texmf-dist/tex/latex/base/atveryend-ltx.sty
Package: atveryend-ltx 2020/08/19 v1.0a Emulation of the original atveryend pac
kage
with kernel methods
)
\Fld@listcount=\count313
\c@bookmark@seq@number=\count314

(/usr/share/texlive/texmf-dist/tex/latex/rerunfilecheck/rerunfilecheck.sty
Package: rerunfilecheck 2022-07-10 v1.10 Rerun checks for auxiliary files (HO)

(/usr/share/texlive/texmf-dist/tex/generic/uniquecounter/uniquecounter.sty
Package: uniquecounter 2019/12/15 v1.4 Provide unlimited unique counter (HO)
)
Package uniquecounter Info: New unique counter `rerunfilecheck' on input line 2
85.
)
\Hy@SectionHShift=\skip75
)
Package hyperref Info: Option `pageanchor' set `false' on input line 63.

(/usr/share/texlive/texmf-dist/tex/latex/geometry/geometry.sty
Package: geometry 2020/01/02 v5.9 Page Geometry

(/usr/share/texlive/texmf-dist/tex/generic/iftex/ifvtex.sty
Package: ifvtex 2019/10/25 v1.7 ifvtex legacy package. Use iftex instead.
)
\Gm@cnth=\count315
\Gm@cntv=\count316
\c@Gm@tempcnt=\count317
\Gm@bindingoffset=\dimen265
\Gm@wd@mp=\dimen266
\Gm@odd@mp=\dimen267
\Gm@even@mp=\dimen268
\Gm@layoutwidth=\dimen269
\Gm@layoutheight=\dimen270
\Gm@layouthoffset=\dimen271
\Gm@layoutvoffset=\dimen272
\Gm@dimlist=\toks35
)
(/usr/share/texlive/texmf-dist/tex/latex/koma-script/scrlayer-scrpage.sty
Package: scrlayer-scrpage 2024/10/24 v3.43 KOMA-Script package (end user interf
ace for scrlayer)

(/usr/share/texlive/texmf-dist/tex/latex/koma-script/scrlayer.sty
Package: scrlayer 2024/10/24 v3.43 KOMA-Script package (defining layers and pag
e styles)
Package scrlayer Info: patching LaTeX kernel macro \pagestyle on input line 218
2.
)
Package scrlayer-scrpage Info: Makeing stand-alone element `pagehead' from
(scrlayer-scrpage)             alias to `pageheadfoot' on input line 1106.
)

Package scrlayer-scrpage Warning: replacing deprecated \clearscrheading by
(scrlayer-scrpage)                \clearmainofpairofpagestyles on input line 12
8.


Package scrlayer-scrpage Warning: Command \deftripstyle is deprecate.
(scrlayer-scrpage)                You should replace it by \deftriplepagestyle,

(scrlayer-scrpage)                e.g., on input line 3424.

) (/usr/share/texlive/texmf-dist/tex/latex/base/textcomp.sty
Package: textcomp 2024/04/24 v2.1b Standard LaTeX package
)
(/usr/share/texlive/texmf-dist/tex/latex/booktabs/booktabs.sty
Package: booktabs 2020/01/12 v1.61803398 Publication quality tables
\heavyrulewidth=\dimen273
\lightrulewidth=\dimen274
\cmidrulewidth=\dimen275
\belowrulesep=\dimen276
\belowbottomsep=\dimen277
\aboverulesep=\dimen278
\abovetopsep=\dimen279
\cmidrulesep=\dimen280
\cmidrulekern=\dimen281
\defaultaddspace=\dimen282
\@cmidla=\count318
\@cmidlb=\count319
\@aboverulesep=\dimen283
\@belowrulesep=\dimen284
\@thisruleclass=\count320
\@lastruleclass=\count321
\@thisrulewidth=\dimen285
)
(/usr/share/texlive/texmf-dist/tex/latex/microtype/microtype.sty
Package: microtype 2025/02/11 v3.2a Micro-typographical refinements (RS)
\MT@toks=\toks36
\MT@tempbox=\box64
\MT@count=\count322
LaTeX Info: Redefining \noprotrusionifhmode on input line 1087.
LaTeX Info: Redefining \leftprotrusion on input line 1088.
\MT@prot@toks=\toks37
LaTeX Info: Redefining \rightprotrusion on input line 1107.
LaTeX Info: Redefining \textls on input line 1449.
\MT@outer@kern=\dimen286
LaTeX Info: Redefining \microtypecontext on input line 2053.
LaTeX Info: Redefining \textmicrotypecontext on input line 2070.
\MT@listname@count=\count323

(/usr/share/texlive/texmf-dist/tex/latex/microtype/microtype-pdftex.def
File: microtype-pdftex.def 2025/02/11 v3.2a Definitions specific to pdftex (RS)

LaTeX Info: Redefining \lsstyle on input line 944.
LaTeX Info: Redefining \lslig on input line 944.
\MT@outer@space=\skip76
)
Package microtype Info: Loading configuration file microtype.cfg.

(/usr/share/texlive/texmf-dist/tex/latex/microtype/microtype.cfg
File: microtype.cfg 2025/02/11 v3.2a microtype main configuration file (RS)
)
LaTeX Info: Redefining \microtypesetup on input line 3065.
)
(/usr/share/texlive/texmf-dist/tex/latex/units/units.sty
Package: units 1998/08/04 v0.9b Typesetting units

(/usr/share/texlive/texmf-dist/tex/latex/base/ifthen.sty
Package: ifthen 2024/03/16 v1.1e Standard LaTeX ifthen package (DPC)
)
(/usr/share/texlive/texmf-dist/tex/latex/units/nicefrac.sty
Package: nicefrac 1998/08/04 v0.9b Nice fractions
\L@UnitsRaiseDisplaystyle=\skip77
\L@UnitsRaiseTextstyle=\skip78
\L@UnitsRaiseScriptstyle=\skip79
))
(/usr/share/texlive/texmf-dist/tex/latex/pgf/frontendlayer/tikz.sty
(/usr/share/texlive/texmf-dist/tex/latex/pgf/utilities/pgffor.sty
(/usr/share/texlive/texmf-dist/tex/latex/pgf/utilities/pgfkeys.sty
(/usr/share/texlive/texmf-dist/tex/generic/pgf/utilities/pgfkeys.code.tex))
(/usr/share/texlive/texmf-dist/tex/latex/pgf/math/pgfmath.sty
(/usr/share/texlive/texmf-dist/tex/generic/pgf/math/pgfmath.code.tex))
(/usr/share/texlive/texmf-dist/tex/generic/pgf/utilities/pgffor.code.tex
Package: pgffor 2023-01-15 v3.1.10 (3.1.10)
\pgffor@iter=\dimen287
\pgffor@skip=\dimen288
\pgffor@stack=\toks38
\pgffor@toks=\toks39
))
(/usr/share/texlive/texmf-dist/tex/generic/pgf/frontendlayer/tikz/tikz.code.tex
Package: tikz 2023-01-15 v3.1.10 (3.1.10)

(/usr/share/texlive/texmf-dist/tex/generic/pgf/libraries/pgflibraryplothandlers
.code.tex
File: pgflibraryplothandlers.code.tex 2023-01-15 v3.1.10 (3.1.10)
\pgf@plot@mark@count=\count324
\pgfplotmarksize=\dimen289
)
\tikz@lastx=\dimen290
\tikz@lasty=\dimen291
\tikz@lastxsaved=\dimen292
\tikz@lastysaved=\dimen293
\tikz@lastmovetox=\dimen294
\tikz@lastmovetoy=\dimen295
\tikzleveldistance=\dimen296
\tikzsiblingdistance=\dimen297
\tikz@figbox=\box65
\tikz@figbox@bg=\box66
\tikz@tempbox=\box67
\tikz@tempbox@bg=\box68
\tikztreelevel=\count325
\tikznumberofchildren=\count326
\tikznumberofcurrentchild=\count327
\tikz@fig@count=\count328

(/usr/share/texlive/texmf-dist/tex/generic/pgf/modules/pgfmodulematrix.code.tex
File: pgfmodulematrix.code.tex 2023-01-15 v3.1.10 (3.1.10)
\pgfmatrixcurrentrow=\count329
\pgfmatrixcurrentcolumn=\count330
\pgf@matrix@numberofcolumns=\count331
)
\tikz@expandcount=\count332

(/usr/share/texlive/texmf-dist/tex/generic/pgf/frontendlayer/tikz/libraries/tik
zlibrarytopaths.code.tex
File: tikzlibrarytopaths.code.tex 2023-01-15 v3.1.10 (3.1.10)
)))
(/usr/share/texlive/texmf-dist/tex/generic/chemfig/chemfig.sty
(/usr/share/texlive/texmf-dist/tex/generic/chemfig/chemfig.tex
(/usr/share/texlive/texmf-dist/tex/generic/simplekv/simplekv.tex
Package: simplekv 2023/10/02 v0.2c Simple keyval package (CT)
)
(/usr/share/texlive/texmf-dist/tex/generic/pgf/libraries/pgflibraryarrows.meta.
code.tex
File: pgflibraryarrows.meta.code.tex 2023-01-15 v3.1.10 (3.1.10)
\pgfarrowinset=\dimen298
\pgfarrowlength=\dimen299
\pgfarrowwidth=\dimen300
\pgfarrowlinewidth=\dimen301
)
\CF_cnt_atomgroup=\count333
\CF_cnt_group=\count334
\CF_cnt_atom=\count335
\CF_cnt_cycle=\count336
\CF_cnt_cyclebonds=\count337
\CF_cnt_compound=\count338
\CF_dim=\dimen302
\CF_turnangle=\dimen303
\CF_arrowsize=\dimen304
\CF_zero=\dimen305
\CF_box=\box69
\CF_boxstuff=\box70
\CF_testbox=\box71
\CF_chargebox=\box72
\CF_substtoks=\toks40
)
Package: chemfig 2023/12/28 v1.66 Draw molecule with an easy syntax (CT)
)
(/usr/share/texlive/texmf-dist/tex/latex/transparent/transparent.sty
Package: transparent 2022-10-27 v1.5 Transparency with color stacks

(/usr/share/texlive/texmf-dist/tex/latex/transparent/transparent-nometadata.sty
Package: transparent-nometadata 2022-10-27 v1.5 Transparency via pdfTeX's color
 stack (HO)
(/usr/share/texlive/texmf-dist/tex/latex/auxhook/auxhook.sty
Package: auxhook 2019-12-17 v1.6 Hooks for auxiliary files (HO)
)))
(/home/<USER>/texmf/tex/latex/caption/caption.sty
Package: caption 2023/08/05 v3.6o Customizing captions (AR)

(/home/<USER>/texmf/tex/latex/caption/caption3.sty
Package: caption3 2023/07/31 v2.4d caption3 kernel (AR)
\caption@tempdima=\dimen306
\captionmargin=\dimen307
\caption@leftmargin=\dimen308
\caption@rightmargin=\dimen309
\caption@width=\dimen310
\caption@indent=\dimen311
\caption@parindent=\dimen312
\caption@hangindent=\dimen313
Package caption Info: KOMA-Script document class detected.

(/home/<USER>/texmf/tex/latex/caption/caption-koma.sto
File: caption-koma.sto 2023/09/08 v2.0e Adaption of the caption package to the 
KOMA-Script document classes (AR)
))
\c@caption@flags=\count339
\c@continuedfloat=\count340
Package caption Info: hyperref package is loaded.
Package caption Info: KOMA-Script scrextend package detected.
\caption@addmargin@hsize=\dimen314
\caption@addmargin@linewidth=\dimen315
)
(/home/<USER>/texmf/tex/latex/caption/subcaption.sty
Package: subcaption 2023/07/28 v1.6b Sub-captions (AR)
Package caption Info: New subtype `subfigure' on input line 238.
\c@subfigure=\count341
Package caption Info: New subtype `subtable' on input line 238.
\c@subtable=\count342
)
(/usr/share/texlive/texmf-dist/tex/latex/float/float.sty
Package: float 2001/11/08 v1.3d Float enhancements (AL)
\c@float@type=\count343
\float@exts=\toks41
\float@box=\box73
\@float@everytoks=\toks42
\@floatcapt=\box74
)
(/usr/share/texlive/texmf-dist/tex/latex/tabularray/tabularray.sty
(/usr/share/texlive/texmf-dist/tex/latex/l3kernel/expl3.sty
Package: expl3 2025-01-18 L3 programming layer (loader) 

(/usr/share/texlive/texmf-dist/tex/latex/l3backend/l3backend-pdftex.def
File: l3backend-pdftex.def 2024-05-08 L3 backend support: PDF output (pdfTeX)
\l__color_backend_stack_int=\count344
\l__pdf_internal_box=\box75
))
Package: tabularray 2024-02-16 v2024A Typeset tabulars and arrays with LaTeX3
\l__tblr_a_int=\count345
\l__tblr_c_int=\count346
\l__tblr_r_int=\count347
\l__tblr_d_dim=\dimen316
\l__tblr_h_dim=\dimen317
\l__tblr_o_dim=\dimen318
\l__tblr_p_dim=\dimen319
\l__tblr_q_dim=\dimen320
\l__tblr_r_dim=\dimen321
\l__tblr_s_dim=\dimen322
\l__tblr_t_dim=\dimen323
\l__tblr_v_dim=\dimen324
\l__tblr_w_dim=\dimen325
\l__tblr_a_box=\box76
\l__tblr_b_box=\box77
\l__tblr_c_box=\box78
\l__tblr_d_box=\box79
\g__tblr_table_count_int=\count348
\c@rownum=\count349
\c@colnum=\count350
\c@rowcount=\count351
\c@colcount=\count352
\abovesep=\dimen326
\belowsep=\dimen327
\leftsep=\dimen328
\rightsep=\dimen329
\g_tblr_level_int=\count353
\g__tblr_data_row_key_count_int=\count354
\g__tblr_data_column_key_count_int=\count355
\g__tblr_data_cell_key_count_int=\count356
\g__tblr_array_int=\count357
\l__tblr_key_count_int=\count358
\l__tblr_key_quotient_int=\count359
\l__tblr_key_quotient_two_int=\count360
\l__tblr_key_remainder_int=\count361
\g__tblr_data_str_value_count_int=\count362
\rulewidth=\dimen330
\l__tblr_strut_dp_dim=\dimen331
\l__tblr_strut_ht_dim=\dimen332
\g__tblr_cell_wd_dim=\dimen333
\g__tblr_cell_ht_dim=\dimen334
\g__tblr_cell_head_dim=\dimen335
\g__tblr_cell_foot_dim=\dimen336
\l__column_target_dim=\dimen337
\l__tblr_caption_box=\box80
\l__tblr_caption_left_box=\box81
\l__tblr_row_head_box=\box82
\l__tblr_row_foot_box=\box83
\l__tblr_row_head_foot_dim=\dimen338
\tablewidth=\dimen339
\l__tblr_table_firsthead_box=\box84
\l__tblr_table_middlehead_box=\box85
\l__tblr_table_lasthead_box=\box86
\l__tblr_table_firstfoot_box=\box87
\l__tblr_table_middlefoot_box=\box88
\l__tblr_table_lastfoot_box=\box89
\l__tblr_remain_height_dim=\dimen340
\l__tblr_long_from_int=\count363
\l__tblr_long_to_int=\count364
\l__tblr_curr_i_int=\count365
\l__tblr_prev_i_int=\count366
\l__tblr_table_page_int=\count367
\l__tblr_table_head_box=\box90
\l__tblr_table_foot_box=\box91
\l__tblr_table_head_foot_dim=\dimen341
\l__tblr_table_head_body_foot_dim=\dimen342
\l__tblr_table_box=\box92
\l__tblr_table_hlines_box=\box93
\l__tblr_hline_box=\box94
\l__tblr_row_box=\box95
\l__tblr_col_o_wd_dim=\dimen343
\l__tblr_col_b_wd_dim=\dimen344
\l__tblr_hline_leftskip_dim=\dimen345
\l__tblr_hline_rightskip_dim=\dimen346
\l__tblr_row_ht_dim=\dimen347
\l__tblr_row_dp_dim=\dimen348
\l__tblr_row_abovesep_dim=\dimen349
\l__tblr_row_belowsep_dim=\dimen350
\l__tblr_row_vlines_box=\box96
\l__tblr_vline_box=\box97
\l__tblr_cell_box=\box98
\l__row_upper_dim=\dimen351
\l__row_lower_dim=\dimen352
\l__row_vpace_dim=\dimen353
\l__tblr_vline_aboveskip_dim=\dimen354
\l__tblr_vline_belowskip_dim=\dimen355
\l__tblr_cell_wd_dim=\dimen356
\l__tblr_cell_ht_dim=\dimen357
\l__tblr_diag_box=\box99
)
(/usr/share/texlive/texmf-dist/tex/latex/graphics/rotating.sty
Package: rotating 2016/08/11 v2.16d rotated objects in LaTeX
\c@r@tfl@t=\count368
\rotFPtop=\skip80
\rotFPbot=\skip81
\rot@float@box=\box100
\rot@mess@toks=\toks43
)
(/usr/share/texlive/texmf-dist/tex/latex/diagbox/diagbox.sty
Package: diagbox 2020/02/09 v2.3 Making table heads with diagonal lines

(/usr/share/texlive/texmf-dist/tex/latex/pict2e/pict2e.sty
Package: pict2e 2020/09/30 v0.4b Improved picture commands (HjG,RN,JT)

(/usr/share/texlive/texmf-dist/tex/latex/pict2e/pict2e.cfg
File: pict2e.cfg 2016/02/05 v0.1u pict2e configuration for teTeX/TeXLive
)
Package pict2e Info: Driver file: pdftex.def on input line 112.
Package pict2e Info: Driver file for pict2e: p2e-pdftex.def on input line 114.

(/usr/share/texlive/texmf-dist/tex/latex/pict2e/p2e-pdftex.def
File: p2e-pdftex.def 2016/02/05 v0.1u Driver-dependant file (RN,HjG,JT)
)
\pIIe@GRAPH=\toks44
\@arclen=\dimen358
\@arcrad=\dimen359
\pIIe@tempdima=\dimen360
\pIIe@tempdimb=\dimen361
\pIIe@tempdimc=\dimen362
\pIIe@tempdimd=\dimen363
\pIIe@tempdime=\dimen364
\pIIe@tempdimf=\dimen365
)
(/usr/share/texlive/texmf-dist/tex/latex/tools/array.sty
Package: array 2024/10/17 v2.6g Tabular extension package (FMi)
\col@sep=\dimen366
\ar@mcellbox=\box101
\extrarowheight=\dimen367
\NC@list=\toks45
\extratabsurround=\skip82
\backup@length=\skip83
\ar@cellbox=\box102
)
\diagbox@boxa=\box103
\diagbox@boxb=\box104
\diagbox@boxm=\box105
\diagbox@wd=\dimen368
\diagbox@ht=\dimen369
\diagbox@insepl=\dimen370
\diagbox@insepr=\dimen371
\diagbox@outsepl=\dimen372
\diagbox@outsepr=\dimen373
)
(/usr/share/texlive/texmf-dist/tex/latex/svg/svg.sty
Package: svg 2020/11/26 v2.02k (include SVG pictures)

(/usr/share/texlive/texmf-dist/tex/latex/trimspaces/trimspaces.sty
Package: trimspaces 2009/09/17 v1.1 Trim spaces around a token list
)
(/usr/share/texlive/texmf-dist/tex/latex/tools/shellesc.sty
Package: shellesc 2023/07/08 v1.0d unified shell escape interface for LaTeX
Package shellesc Info: Restricted shell escape enabled on input line 77.
)
\c@svg@param@lastpage=\count369
\svg@box=\box106
\c@svg@param@currpage=\count370
)
(/usr/share/texlive/texmf-dist/tex/latex/base/fontenc.sty
Package: fontenc 2021/04/29 v2.0v Standard LaTeX package
LaTeX Font Info:    Trying to load font information for T1+phv on input line 11
6.

(/usr/share/texlive/texmf-dist/tex/latex/psnfss/t1phv.fd
File: t1phv.fd 2020/03/25 scalable font definitions for T1/phv.
)
LaTeX Font Info:    Font shape `T1/phv/m/n' will be
(Font)              scaled to size 11.68495pt on input line 116.
)

LaTeX Warning: Unused global option(s):
    [DIV].

(out/Main.aux (out/Kapitel/Abkürzungen.aux) (out/Kapitel/01.Einleitung.aux)
(out/Kapitel/02.Grundlagen.aux) (out/Kapitel/03.StandderTechnik.aux)
(out/Kapitel/04.EigenerAnsatz.aux) (out/Kapitel/05.DiskussionUndBewertung.aux)
(out/Kapitel/06.Anwendung.aux) (out/Kapitel/07.ZusammenfassungUndAusblick.aux))
\openout1 = `Main.aux'.

LaTeX Font Info:    Checking defaults for OML/cmm/m/it on input line 81.
LaTeX Font Info:    ... okay on input line 81.
LaTeX Font Info:    Checking defaults for OMS/cmsy/m/n on input line 81.
LaTeX Font Info:    ... okay on input line 81.
LaTeX Font Info:    Checking defaults for OT1/cmr/m/n on input line 81.
LaTeX Font Info:    ... okay on input line 81.
LaTeX Font Info:    Checking defaults for T1/cmr/m/n on input line 81.
LaTeX Font Info:    ... okay on input line 81.
LaTeX Font Info:    Checking defaults for TS1/cmr/m/n on input line 81.
LaTeX Font Info:    ... okay on input line 81.
LaTeX Font Info:    Checking defaults for OMX/cmex/m/n on input line 81.
LaTeX Font Info:    ... okay on input line 81.
LaTeX Font Info:    Checking defaults for U/cmr/m/n on input line 81.
LaTeX Font Info:    ... okay on input line 81.
LaTeX Font Info:    Checking defaults for PD1/pdf/m/n on input line 81.
LaTeX Font Info:    ... okay on input line 81.
LaTeX Font Info:    Checking defaults for PU/pdf/m/n on input line 81.
LaTeX Font Info:    ... okay on input line 81.
(/usr/share/texlive/texmf-dist/tex/context/base/mkii/supp-pdf.mkii
[Loading MPS to PDF converter (version 2006.09.02).]
\scratchcounter=\count371
\scratchdimen=\dimen374
\scratchbox=\box107
\nofMPsegments=\count372
\nofMParguments=\count373
\everyMPshowfont=\toks46
\MPscratchCnt=\count374
\MPscratchDim=\dimen375
\MPnumerator=\count375
\makeMPintoPDFobject=\count376
\everyMPtoPDFconversion=\toks47
) (/usr/share/texlive/texmf-dist/tex/latex/epstopdf-pkg/epstopdf-base.sty
Package: epstopdf-base 2020-01-24 v2.11 Base part for package epstopdf

(/usr/share/texlive/texmf-dist/tex/latex/grfext/grfext.sty
Package: grfext 2019/12/03 v1.3 Manage graphics extensions (HO)
)
Package epstopdf-base Info: Redefining graphics rule for `.eps' on input line 4
85.
Package grfext Info: Graphics extension search list:
(grfext)             [.pdf,.png,.jpg,.mps,.jpeg,.jbig2,.jb2,.PDF,.PNG,.JPG,.JPE
G,.JBIG2,.JB2,.eps]
(grfext)             \AppendGraphicsExtensions on input line 504.

(/usr/share/texlive/texmf-dist/tex/latex/latexconfig/epstopdf-sys.cfg
File: epstopdf-sys.cfg 2010/07/13 v1.3 Configuration of (r)epstopdf for TeX Liv
e
))
Package hyperref Info: Link coloring OFF on input line 81.

(out/Main.out) (out/Main.out)
\@outlinefile=\write4
\openout4 = `Main.out'.

Class scrbook Info: loading recommended package `bookmark'.
(scrbook)           Using `bookmark' together with `hyperref' is recommended,
(scrbook)           because of handling of possible bookmark level gaps.
(scrbook)           You can avoid loading `bookmark' with KOMA-Script option
(scrbook)           `bookmarkpackage=false' before \begin{document} and
(scrbook)           you can avoid this message adding:
(scrbook)             \usepackage{bookmark}
(scrbook)           before \begin{document} on input line 81.

(/usr/share/texlive/texmf-dist/tex/latex/bookmark/bookmark.sty
Package: bookmark 2023-12-10 v1.31 PDF bookmarks (HO)

(/usr/share/texlive/texmf-dist/tex/latex/bookmark/bkm-pdftex.def
File: bkm-pdftex.def 2023-12-10 v1.31 bookmark driver for pdfTeX and luaTeX (HO
)
\BKM@id=\count377
))
*geometry* driver: auto-detecting
*geometry* detected driver: pdftex
*geometry* verbose mode - [ preamble ] result:
* driver: pdftex
* paper: <default>
* layout: <same size as paper>
* layoutoffset:(h,v)=(0.0pt,0.0pt)
* modes: includefoot 
* h-part:(L,W,R)=(56.9055pt, 497.92331pt, 42.67912pt)
* v-part:(T,H,B)=(96.73936pt, 691.40207pt, 56.9055pt)
* \paperwidth=597.50793pt
* \paperheight=845.04694pt
* \textwidth=497.92331pt
* \textheight=657.25877pt
* \oddsidemargin=-15.36449pt
* \evensidemargin=-15.36449pt
* \topmargin=-31.13809pt
* \headheight=30.0pt
* \headsep=25.60747pt
* \topskip=12.3pt
* \footskip=34.1433pt
* \marginparwidth=66.38977pt
* \marginparsep=12.8401pt
* \columnsep=10.0pt
* \skip\footins=11.06996pt plus 4.91945pt minus 2.46051pt
* \hoffset=0.0pt
* \voffset=0.0pt
* \mag=1000
* \@twocolumnfalse
* \@twosidefalse
* \@mparswitchfalse
* \@reversemarginfalse
* (1in=72.27pt=25.4mm, 1cm=28.453pt)

LaTeX Info: Redefining \microtypecontext on input line 81.
Package microtype Info: Applying patch `item' on input line 81.
Package microtype Info: Applying patch `toc' on input line 81.
Package microtype Info: The `eqnum' patch may not be effective because you are
(microtype)             using the mathtools package. Make sure to insert
(microtype)             `\leftprotrusion' and `\rightprotrusion' as
(microtype)             appropriate in mathtools's `\newtagform' command.
Package microtype Info: Applying patch `eqnum' on input line 81.
Package microtype Info: Applying patch `footnote' on input line 81.
Package microtype Info: Applying patch `verbatim' on input line 81.
LaTeX Info: Redefining \microtypesetup on input line 81.
Package microtype Info: Generating PDF output.
Package microtype Info: Character protrusion enabled (level 2).
Package microtype Info: Using default protrusion set `alltext'.
Package microtype Info: Automatic font expansion enabled (level 2),
(microtype)             stretch: 20, shrink: 20, step: 1, non-selected.
Package microtype Info: Using default expansion set `alltext-nott'.
LaTeX Info: Redefining \showhyphens on input line 81.
Package microtype Info: No adjustment of tracking.
Package microtype Info: No adjustment of interword spacing.
Package microtype Info: No adjustment of character kerning.
Package microtype Info: Loading generic protrusion settings for font family
(microtype)             `phv' (encoding: T1).
(microtype)             For optimal results, create family-specific settings.
(microtype)             See the microtype manual for details.
Package caption Info: Begin \AtBeginDocument code.
Package caption Info: float package is loaded.
Package caption Info: rotating package is loaded.
Package caption Info: End \AtBeginDocument code.
(/usr/share/texlive/texmf-dist/tex/latex/ninecolors/ninecolors.sty
Package: ninecolors 2022-02-13 v2022D Select colors with proper color contrast

(/usr/share/texlive/texmf-dist/tex/latex/l3packages/xparse/xparse.sty
Package: xparse 2024-08-16 L3 Experimental document command parser
))
LaTeX Font Info:    Font shape `T1/phv/m/n' will be
(Font)              scaled to size 15.19995pt on input line 86.
LaTeX Font Info:    Font shape `T1/phv/b/n' will be
(Font)              scaled to size 22.79993pt on input line 86.
LaTeX Font Info:    Font shape `T1/phv/m/n' will be
(Font)              scaled to size 18.99994pt on input line 86.
LaTeX Font Info:    Font shape `T1/phv/m/n' will be
(Font)              scaled to size 11.39996pt on input line 86.
LaTeX Font Info:    Font shape `T1/phv/m/it' in size <12> not available
(Font)              Font shape `T1/phv/m/sl' tried instead on input line 86.
LaTeX Font Info:    Font shape `T1/phv/m/sl' will be
(Font)              scaled to size 11.39996pt on input line 86.
LaTeX Font Info:    Font shape `T1/phv/b/n' will be
(Font)              scaled to size 17.09995pt on input line 86.
LaTeX Font Info:    Font shape `T1/phv/m/n' will be
(Font)              scaled to size 12.34996pt on input line 86.
LaTeX Font Info:    Font shape `T1/phv/b/n' will be
(Font)              scaled to size 12.34996pt on input line 86.


[1



{/var/lib/texmf/fonts/map/pdftex/updmap/pdftex.map}{/usr/share/texlive/texmf-di
st/fonts/enc/dvips/base/8r.enc}]

[2



]
LaTeX Font Info:    Font shape `T1/phv/b/n' will be
(Font)              scaled to size 11.68495pt on input line 98.
LaTeX Font Info:    Font shape `T1/phv/b/n' will be
(Font)              scaled to size 18.04994pt on input line 98.
LaTeX Font Info:    Calculating math sizes for size <12.3> on input line 98.
LaTeX Font Info:    Trying to load font information for OT1+lmr on input line 9
8.
 (/usr/share/texmf/tex/latex/lm/ot1lmr.fd
File: ot1lmr.fd 2015/05/01 v1.6.1 Font defs for Latin Modern
)
(/usr/share/texlive/texmf-dist/tex/latex/microtype/mt-cmr.cfg
File: mt-cmr.cfg 2013/05/19 v2.2 microtype config. file: Computer Modern Roman 
(RS)
)
LaTeX Font Info:    Trying to load font information for OML+lmm on input line 9
8.

(/usr/share/texmf/tex/latex/lm/omllmm.fd
File: omllmm.fd 2015/05/01 v1.6.1 Font defs for Latin Modern
)
LaTeX Font Info:    Trying to load font information for OMS+lmsy on input line 
98.

(/usr/share/texmf/tex/latex/lm/omslmsy.fd
File: omslmsy.fd 2015/05/01 v1.6.1 Font defs for Latin Modern
)
LaTeX Font Info:    Trying to load font information for OMX+lmex on input line 
98.

(/usr/share/texmf/tex/latex/lm/omxlmex.fd
File: omxlmex.fd 2015/05/01 v1.6.1 Font defs for Latin Modern
)
LaTeX Font Info:    External font `lmex10' loaded for size
(Font)              <12.3> on input line 98.
LaTeX Font Info:    External font `lmex10' loaded for size
(Font)              <8.60995> on input line 98.
LaTeX Font Info:    External font `lmex10' loaded for size
(Font)              <6.15> on input line 98.


[3]

[4


]

[5


]

[6


]
Package tocbasic Info: character protrusion at toc deactivated on input line 12
5.
 (out/Main.toc)
\tf@toc=\write5
\openout5 = `Main.toc'.



LaTeX Font Info:    Font shape `T1/phv/m/n' will be
(Font)              scaled to size 10.44997pt on input line 127.
LaTeX Font Info:    Calculating math sizes for size <11> on input line 127.
LaTeX Font Info:    External font `lmex10' loaded for size
(Font)              <11> on input line 127.
LaTeX Font Info:    External font `lmex10' loaded for size
(Font)              <7.69997> on input line 127.
LaTeX Font Info:    External font `lmex10' loaded for size
(Font)              <5.5> on input line 127.

Package scrlayer-scrpage Warning: \headheight to low.
(scrlayer-scrpage)                At least 31.1992pt needed,
(scrlayer-scrpage)                but only 30.0pt found.
(scrlayer-scrpage)                I'll enlarge \headheight, for further
(scrlayer-scrpage)                processing, but you should do this yourself,
(scrlayer-scrpage)                e.g., setting geometry's option
(scrlayer-scrpage)                `head=31.1992pt'.
(scrlayer-scrpage)                I'll also decrease \topmargin on input line 1
27.


Overfull \vbox (1.1992pt too high) has occurred while \output is active []


[1




]
\openout2 = `Kapitel/Abkürzungen.aux'.

 (./Kapitel/Abkürzungen.tex
chapter without number
LaTeX Font Info:    Font shape `T1/phv/m/n' will be
(Font)              scaled to size 18.04994pt on input line 1.
> Step: init table outer spec.
> Step: parse table options.
> Step: split table.
> Step: init table inner spec.
> Step: parse table inner spec.
> Step: execute table commands.
> Step: calculate cell and line sizes.
LaTeX Font Info:    Font shape `T1/phv/m/n' will be
(Font)              scaled to size 8.17943pt on input line 34.
LaTeX Font Info:    Font shape `T1/phv/m/n' will be
(Font)              scaled to size 5.84247pt on input line 34.
LaTeX Font Info:    Font shape `T1/phv/m/it' in size <12.3> not available
(Font)              Font shape `T1/phv/m/sl' tried instead on input line 34.
LaTeX Font Info:    Font shape `T1/phv/m/sl' will be
(Font)              scaled to size 11.68495pt on input line 34.
> Step: build the whole table.
)

[2


{/usr/share/texmf/fonts/enc/dvips/lm/lm-mathit.enc}{/usr/share/texmf/fonts/enc/
dvips/lm/lm-mathsy.enc}{/usr/share/texmf/fonts/enc/dvips/lm/lm-rm.enc}]
\openout2 = `Kapitel/01.Einleitung.aux'.


(./Kapitel/01.Einleitung.tex
chapter 1.
LaTeX Font Info:    Font shape `T1/phv/b/n' will be
(Font)              scaled to size 15.19995pt on input line 7.
)

[1





]
\openout2 = `Kapitel/02.Grundlagen.aux'.

 (./Kapitel/02.Grundlagen.tex
chapter 2.
)

[2




]
\openout2 = `Kapitel/03.StandderTechnik.aux'.

 (./Kapitel/03.StandderTechnik.tex
chapter 3.
)

[3




]
\openout2 = `Kapitel/04.EigenerAnsatz.aux'.

 (./Kapitel/04.EigenerAnsatz.tex
chapter 4.
)

[4




]
\openout2 = `Kapitel/05.DiskussionUndBewertung.aux'.

 (./Kapitel/05.DiskussionUndBewertung.tex
chapter 5.
)

[5




]
\openout2 = `Kapitel/06.Anwendung.aux'.

 (./Kapitel/06.Anwendung.tex
chapter 6.
)

[6




]
\openout2 = `Kapitel/07.ZusammenfassungUndAusblick.aux'.

 (./Kapitel/07.ZusammenfassungUndAusblick.tex
chapter 7.
)

[7




]
Package tocbasic Info: character protrusion at lof deactivated on input line 15
5.
 (out/Main.lof)
\tf@lof=\write6
\openout6 = `Main.lof'.



[1




]
Package tocbasic Info: character protrusion at lot deactivated on input line 16
5.
 (out/Main.lot)
\tf@lot=\write7
\openout7 = `Main.lot'.



[2

] (out/Main.bbl

Package natbib Warning: Empty `thebibliography' environment on input line 9.

)

[3



]

[4]

Package caption Warning: Unused \captionsetup[figure] on input line 49.
See the caption package documentation for explanation.

(out/Main.aux (out/Kapitel/Abkürzungen.aux) (out/Kapitel/01.Einleitung.aux)
(out/Kapitel/02.Grundlagen.aux) (out/Kapitel/03.StandderTechnik.aux)
(out/Kapitel/04.EigenerAnsatz.aux) (out/Kapitel/05.DiskussionUndBewertung.aux)
(out/Kapitel/06.Anwendung.aux) (out/Kapitel/07.ZusammenfassungUndAusblick.aux))
 ***********
LaTeX2e <2024-11-01> patch level 2
L3 programming layer <2025-01-18>
 ***********
Package rerunfilecheck Info: File `Main.out' has not changed.
(rerunfilecheck)             Checksum: D41D8CD98F00B204E9800998ECF8427E;0.
 ) 
Here is how much of TeX's memory you used:
 37009 strings out of 475084
 764122 string characters out of 5763116
 1539144 words of memory out of 5000000
 59226 multiletter control sequences out of 15000+600000
 632094 words of font info for 166 fonts, out of 8000000 for 9000
 36 hyphenation exceptions out of 8191
 122i,21n,121p,10900b,1387s stack positions out of 10000i,1000n,20000p,200000b,200000s
</usr/share/texmf/fonts/type1/public/lm/lmmi9.pfb></usr/share/texmf/fonts/typ
e1/public/lm/lmr9.pfb></usr/share/texmf/fonts/type1/public/lm/lmsy9.pfb></usr/s
hare/texlive/texmf-dist/fonts/type1/urw/helvetic/uhvb8a.pfb></usr/share/texlive
/texmf-dist/fonts/type1/urw/helvetic/uhvr8a.pfb></usr/share/texlive/texmf-dist/
fonts/type1/urw/helvetic/uhvro8a.pfb>
Output written on out/Main.pdf (19 pages, 88373 bytes).
PDF statistics:
 194 PDF objects out of 1000 (max. 8388607)
 159 compressed objects within 2 object streams
 22 named destinations out of 1000 (max. 500000)
 71813 words of extra memory for PDF output out of 74296 (max. 10000000)

